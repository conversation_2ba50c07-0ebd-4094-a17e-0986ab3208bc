#!/usr/bin/env python3
"""
测试并发处理改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chapter_counting():
    """测试章节计数逻辑"""
    print("=== 测试章节计数逻辑 ===")

    try:
        from app import create_app
        from app.tasks import ProgressAwareTextRewriter
        from pathlib import Path
        import tempfile
        import shutil
        
        app = create_app()
        
        with app.app_context():
            # 创建临时测试目录
            temp_dir = Path(tempfile.mkdtemp())
            input_dir = temp_dir / "chapters"
            output_dir = temp_dir / "output"
            input_dir.mkdir()
            output_dir.mkdir()
            
            try:
                # 创建5个测试章节文件
                for i in range(1, 6):
                    chapter_file = input_dir / f"chapter_{i:03d}.txt"
                    chapter_file.write_text(f"""第{i}章 测试章节

这是第{i}章的内容。
主角在这一章中经历了新的冒险。

故事继续发展，情节越来越精彩。
""", encoding='utf-8')
                
                # 模拟已处理的章节（创建部分输出文件）
                for i in [1, 3]:  # 模拟第1章和第3章已处理
                    output_file = output_dir / f"chapter_{i:03d}_rewrite.txt"
                    output_file.write_text(f"第{i}章已优化内容", encoding='utf-8')
                
                print(f"创建了5个章节文件，其中第1、3章已处理")
                
                # 创建TextRewriter实例
                api_keys = ['test-key']
                rewriter = ProgressAwareTextRewriter(
                    api_keys=api_keys,
                    character='测试主角',
                    book_name='测试小说',
                    channel='男频',
                    person='三',
                    key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
                )
                
                # 测试进度回调
                progress_log = []
                def test_progress_callback(current, total, status):
                    progress_log.append((current, total, status))
                    print(f"进度: {current}/{total} - {status}")
                
                # 模拟处理（不实际调用API）
                print("\n开始测试章节计数...")
                
                # 获取所有.txt文件但排除包含rewrite的文件
                import re
                input_files = sorted(
                    [f for f in input_dir.glob('*.txt') if 'rewrite' not in f.stem],
                    key=lambda x: int(re.search(r'\d+', x.stem).group())
                )
                
                total_chapters = len(input_files)
                already_processed = 0
                pending_files = []
                
                for file_path in input_files:
                    chapter_num = int(re.search(r'\d+', file_path.stem).group())
                    output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
                    output_file_path = output_dir / output_name
                    
                    if output_file_path.exists():
                        print(f"跳过已处理的第{chapter_num}章")
                        already_processed += 1
                    else:
                        pending_files.append(file_path)
                        print(f"待处理第{chapter_num}章")
                
                print(f"\n统计结果:")
                print(f"总章节数: {total_chapters}")
                print(f"已处理章节: {already_processed}")
                print(f"待处理章节: {len(pending_files)}")
                
                # 验证计数逻辑
                assert total_chapters == 5, f"总章节数应为5，实际为{total_chapters}"
                assert already_processed == 2, f"已处理章节应为2，实际为{already_processed}"
                assert len(pending_files) == 3, f"待处理章节应为3，实际为{len(pending_files)}"
                
                print("✓ 章节计数逻辑正确")
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_dir)
                
    except Exception as e:
        print(f"章节计数测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_smart_context_extraction():
    """测试智能上下文提取"""
    print("\n=== 测试智能上下文提取 ===")
    
    try:
        from app import create_app
        from app.tasks import ProgressAwareTextRewriter
        
        app = create_app()
        
        with app.app_context():
            # 创建TextRewriter实例
            api_keys = ['test-key']
            rewriter = ProgressAwareTextRewriter(
                api_keys=api_keys,
                character='测试主角',
                book_name='测试小说',
                channel='男频',
                person='三',
                key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
            )
            
            # 测试文本
            test_text = """第一段内容，这里是故事的开始部分。主角刚刚踏上了冒险的旅程。

第二段内容，情节开始发展。主角遇到了第一个挑战，需要想办法解决。

第三段内容，这是故事的高潮部分。主角面临着前所未有的困难，但他没有放弃。

第四段内容，故事接近尾声。主角通过自己的努力，终于克服了所有的困难。

第五段内容，这是本章的结尾。主角获得了成长，为下一章的冒险做好了准备。"""
            
            # 测试智能提取结尾
            context = rewriter._extract_smart_ending(test_text)
            print(f"原文长度: {len(test_text)}字")
            print(f"提取的上下文长度: {len(context)}字")
            print(f"提取的上下文:\n{context}")
            
            # 验证提取结果
            assert len(context) <= 1000, f"上下文长度应不超过1000字，实际为{len(context)}字"
            assert "第五段内容" in context, "应该包含最后一段"
            assert "第四段内容" in context, "应该包含倒数第二段"
            
            print("✓ 智能上下文提取正确")
            
            # 测试超长文本
            long_text = "很长的段落内容。" * 200  # 创建一个很长的段落
            long_context = rewriter._extract_smart_ending(long_text)
            print(f"\n长文本测试:")
            print(f"原文长度: {len(long_text)}字")
            print(f"提取的上下文长度: {len(long_context)}字")
            
            assert len(long_context) <= 1000, f"长文本上下文长度应不超过1000字，实际为{len(long_context)}字"
            print("✓ 长文本处理正确")
            
    except Exception as e:
        print(f"智能上下文提取测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_dependency_logic():
    """测试章节依赖逻辑"""
    print("\n=== 测试章节依赖逻辑 ===")
    
    try:
        from app import create_app
        from app.tasks import ProgressAwareTextRewriter
        from pathlib import Path
        import tempfile
        import shutil
        
        app = create_app()
        
        with app.app_context():
            # 创建临时测试目录
            temp_dir = Path(tempfile.mkdtemp())
            output_dir = temp_dir / "output"
            output_dir.mkdir()
            
            try:
                # 创建TextRewriter实例
                api_keys = ['test-key']
                rewriter = ProgressAwareTextRewriter(
                    api_keys=api_keys,
                    character='测试主角',
                    book_name='测试小说',
                    channel='男频',
                    person='三',
                    key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
                )
                
                # 测试等待前一章的逻辑
                completed_chapters = set()
                
                # 测试1: 第1章不需要等待
                result = rewriter._wait_for_previous_chapter(0, output_dir, completed_chapters, timeout=1)
                print(f"第1章等待结果: {result}")
                assert result == True, "第1章不应该需要等待"
                
                # 测试2: 前一章已完成
                completed_chapters.add(1)
                result = rewriter._wait_for_previous_chapter(1, output_dir, completed_chapters, timeout=1)
                print(f"前一章已完成等待结果: {result}")
                assert result == True, "前一章已完成应该立即返回"
                
                # 测试3: 前一章文件存在
                chapter_1_file = output_dir / "chapter_001_rewrite.txt"
                chapter_1_file.write_text("第1章内容", encoding='utf-8')
                completed_chapters.clear()
                result = rewriter._wait_for_previous_chapter(1, output_dir, completed_chapters, timeout=1)
                print(f"前一章文件存在等待结果: {result}")
                assert result == True, "前一章文件存在应该立即返回"
                assert 1 in completed_chapters, "应该将前一章标记为已完成"
                
                print("✓ 章节依赖逻辑正确")
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_dir)
                
    except Exception as e:
        print(f"章节依赖逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chapter_counting()
    test_smart_context_extraction()
    test_dependency_logic()
    print("\n=== 并发改进测试完成 ===")
