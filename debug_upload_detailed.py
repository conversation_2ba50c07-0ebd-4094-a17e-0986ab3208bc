#!/usr/bin/env python3
"""
详细调试上传功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_upload_route():
    """测试上传路由"""
    print("=== 测试上传路由 ===")
    
    try:
        from app import create_app
        from io import BytesIO
        
        app = create_app()
        
        with app.test_client() as client:
            # 首先登录
            login_response = client.post('/login', data={
                'username': 'admin',
                'password': 'password123'
            }, follow_redirects=True)
            
            print(f"登录响应状态: {login_response.status_code}")
            
            # 获取上传页面
            upload_page = client.get('/upload')
            print(f"上传页面状态: {upload_page.status_code}")
            
            if upload_page.status_code == 200:
                print("✓ 上传页面可访问")
                
                # 检查页面内容
                page_content = upload_page.get_data(as_text=True)
                if 'csrf_token' in page_content:
                    print("✓ 页面包含CSRF token")
                else:
                    print("✗ 页面缺少CSRF token")
                    
                # 提取CSRF token
                import re
                csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', page_content)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"✓ 提取到CSRF token: {csrf_token[:10]}...")
                    
                    # 创建测试文件
                    file_content = """第一章 测试开始

这是一个测试文件的内容。
主角开始了他的冒险之旅。

第二章 测试继续

故事继续发展。
主角遇到了新的挑战。
"""
                    
                    # 模拟文件上传
                    data = {
                        'csrf_token': csrf_token,
                        'character': '测试主角',
                        'book_name': '测试小说',
                        'channel': '男频',
                        'person': '三',
                        'submit': '开始优化'
                    }
                    
                    files = {
                        'file': (BytesIO(file_content.encode('utf-8')), 'test_novel.txt', 'text/plain')
                    }
                    
                    # 提交表单
                    upload_response = client.post('/upload', 
                                                data=data, 
                                                content_type='multipart/form-data',
                                                buffered=True)
                    
                    print(f"上传响应状态: {upload_response.status_code}")
                    print(f"上传响应位置: {upload_response.location if upload_response.status_code == 302 else 'N/A'}")
                    
                    if upload_response.status_code == 302:
                        print("✓ 上传成功，重定向到任务详情页")
                    else:
                        print("✗ 上传失败")
                        response_text = upload_response.get_data(as_text=True)
                        print(f"响应内容: {response_text[:500]}...")
                        
                else:
                    print("✗ 无法提取CSRF token")
            else:
                print("✗ 上传页面不可访问")
                
    except Exception as e:
        print(f"上传路由测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_form_with_csrf():
    """测试带CSRF的表单"""
    print("\n=== 测试带CSRF的表单 ===")
    
    try:
        from app import create_app
        from app.forms import UploadForm
        from werkzeug.datastructures import FileStorage
        from io import BytesIO
        
        app = create_app()
        
        with app.test_request_context('/', method='POST'):
            # 创建模拟文件
            file_data = BytesIO(b"This is a test file content")
            file_storage = FileStorage(
                stream=file_data,
                filename="test.txt",
                content_type="text/plain"
            )
            
            # 创建表单数据（包含CSRF token）
            form_data = {
                'character': '测试主角',
                'book_name': '测试书名',
                'channel': '男频',
                'person': '三',
                'submit': True,
                'csrf_token': 'test-token'  # 在测试环境中可能需要禁用CSRF
            }
            
            # 创建表单
            form = UploadForm(data=form_data)
            form.file.data = file_storage
            
            # 禁用CSRF验证进行测试
            form.csrf_token.data = form.csrf_token.current_token
            
            print(f"表单验证结果: {form.validate()}")
            if form.errors:
                print(f"表单错误: {form.errors}")
            else:
                print("✓ 表单验证通过")
                
    except Exception as e:
        print(f"CSRF表单测试失败: {e}")

def test_celery_connection():
    """测试Celery连接"""
    print("\n=== 测试Celery连接 ===")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            from app import celery
            
            # 检查Celery配置
            print(f"Celery broker: {celery.conf.broker_url}")
            print(f"Celery backend: {celery.conf.result_backend}")
            
            # 测试Celery连接
            try:
                # 发送一个简单的ping任务
                result = celery.control.ping(timeout=1)
                if result:
                    print("✓ Celery连接正常")
                    print(f"活跃的worker: {list(result[0].keys()) if result else 'None'}")
                else:
                    print("✗ 没有活跃的Celery worker")
            except Exception as e:
                print(f"✗ Celery连接失败: {e}")
                
    except Exception as e:
        print(f"Celery测试失败: {e}")

def check_logs():
    """检查日志文件"""
    print("\n=== 检查日志 ===")
    
    # 检查Flask应用日志
    log_files = [
        'app.log',
        'celery.log',
        'error.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"发现日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"  最后几行:")
                        for line in lines[-5:]:
                            print(f"    {line.strip()}")
                    else:
                        print(f"  文件为空")
            except Exception as e:
                print(f"  读取失败: {e}")
        else:
            print(f"日志文件不存在: {log_file}")

if __name__ == "__main__":
    test_upload_route()
    test_form_with_csrf()
    test_celery_connection()
    check_logs()
    print("\n=== 详细调试完成 ===")
