import os
import re
import uuid
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import current_app

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, user_id):
    """保存上传的文件并返回文件路径"""
    if file and allowed_file(file.filename):
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加UUID前缀避免文件名冲突
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # 创建用户专属目录
        user_dir = Path(current_app.config['UPLOAD_FOLDER']) / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        file_path = user_dir / unique_filename
        file.save(str(file_path))
        
        return str(file_path)
    return None

def split_text_into_chapters(text_content):
    """将文本内容分割成章节"""
    chapters = []

    # 更全面的章节标题模式
    chapter_patterns = [
        r'第[一二三四五六七八九十百千万\d]+章[^\n]*',  # 第X章 + 可能的章节标题
        r'第[0-9]+章[^\n]*',  # 第数字章 + 可能的章节标题
        r'Chapter\s*\d+[^\n]*',  # Chapter数字 + 可能的标题
        r'[第]?[0-9]+[章节][^\n]*',  # 数字章/节 + 可能的标题
        r'[第]?[一二三四五六七八九十百千万]+[章节][^\n]*',  # 中文数字章/节 + 可能的标题
    ]

    # 尝试每个模式
    for pattern in chapter_patterns:
        # 查找所有章节标题
        chapter_titles = re.findall(pattern, text_content, flags=re.IGNORECASE | re.MULTILINE)

        if len(chapter_titles) > 1:  # 找到了多个章节
            # 按章节标题分割文本
            parts = re.split(f'({pattern})', text_content, flags=re.IGNORECASE | re.MULTILINE)

            current_title = None
            current_content = []
            chapter_num = 1

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 检查是否是章节标题
                if re.match(pattern, part, flags=re.IGNORECASE | re.MULTILINE):
                    # 保存前一章
                    if current_title and current_content:
                        content_text = '\n\n'.join(current_content).strip()
                        if content_text:  # 只有内容不为空才添加
                            chapters.append({
                                'number': chapter_num,
                                'title': current_title,
                                'content': content_text
                            })
                            chapter_num += 1

                    # 开始新章
                    current_title = part
                    current_content = []
                else:
                    # 这是章节内容
                    if part.strip():
                        current_content.append(part)

            # 添加最后一章
            if current_title and current_content:
                content_text = '\n\n'.join(current_content).strip()
                if content_text:
                    chapters.append({
                        'number': chapter_num,
                        'title': current_title,
                        'content': content_text
                    })

            # 如果找到了章节，就返回结果
            if chapters:
                break

    # 如果没有找到章节标题，按内容长度智能分割
    if not chapters:
        # 按段落分割
        paragraphs = [p.strip() for p in re.split(r'\n\s*\n', text_content) if p.strip()]

        if len(paragraphs) <= 1:
            # 内容太少，整体作为一章
            chapters.append({
                'number': 1,
                'title': '第1章',
                'content': text_content.strip()
            })
        elif len(paragraphs) <= 10:
            # 段落较少，每个段落作为一章
            for i, para in enumerate(paragraphs, 1):
                chapters.append({
                    'number': i,
                    'title': f'第{i}章',
                    'content': para
                })
        else:
            # 段落较多，按字数智能分组
            total_chars = len(text_content)
            target_chapter_length = max(1000, total_chars // 20)  # 目标每章1000字或总长度的1/20

            current_chapter = []
            current_length = 0
            chapter_num = 1

            for para in paragraphs:
                para_length = len(para)

                # 如果加上这个段落会超过目标长度，且当前章节不为空，则开始新章
                if current_length + para_length > target_chapter_length and current_chapter:
                    chapter_content = '\n\n'.join(current_chapter)
                    chapters.append({
                        'number': chapter_num,
                        'title': f'第{chapter_num}章',
                        'content': chapter_content
                    })
                    chapter_num += 1
                    current_chapter = [para]
                    current_length = para_length
                else:
                    current_chapter.append(para)
                    current_length += para_length

            # 添加最后一章
            if current_chapter:
                chapter_content = '\n\n'.join(current_chapter)
                chapters.append({
                    'number': chapter_num,
                    'title': f'第{chapter_num}章',
                    'content': chapter_content
                })

    return chapters

def create_chapter_files(chapters, base_path):
    """为每个章节创建单独的文件"""
    chapter_files = []
    base_dir = Path(base_path).parent / f"{Path(base_path).stem}_chapters"
    base_dir.mkdir(parents=True, exist_ok=True)
    
    for chapter in chapters:
        chapter_filename = f"chapter_{chapter['number']:03d}.txt"
        chapter_path = base_dir / chapter_filename
        
        with open(chapter_path, 'w', encoding='utf-8') as f:
            f.write(chapter['content'])
        
        chapter_files.append(str(chapter_path))
    
    return str(base_dir), chapter_files

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / (1024 * 1024), 2)
    except:
        return 0
