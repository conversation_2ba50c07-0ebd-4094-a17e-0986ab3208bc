import logging
import re
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from difflib import SequenceMatcher
from concurrent.futures import ThreadPoolExecutor, as_completed
from flask import current_app
from openai import OpenAI
from jinja2 import Environment, FileSystemLoader
from app import celery, db
from app.models import AdaptationTask
from app.utils import split_text_into_chapters, create_chapter_files

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BlockError(Exception):
    pass

def is_allowed_character(char: str) -> bool:
    """检查字符是否为允许的字符类型（数字、英文、中文）"""
    if not char or char.isspace():
        return True

    # 检查数字
    if char.isdigit():
        return True

    # 检查英文字母
    if 'a' <= char.lower() <= 'z':
        return True

    # 检查中文字符
    if '\u4e00' <= char <= '\u9fff':
        return True

    # 标点符号等常见字符
    common_symbols ={'。', ' ', "'", '♀', ')', '€', '№', '∞', '℉', '&', '￥', '"', '=', '`', '《', '°', '.', '}', '¤', '|', '®', '「', '；', '*', '–', ':', '】', '{', '<', '¢', '\x0b', '…', '％', '℃', '~', '‱', '?', '》', '\r', '‘', '[', '，', '」', '＝', '”', '©', '(', '、', '：', '【', '—', '（', '）', '\t', '“', '$', '/', '♂', '？', '‰', '-', '\\', '™', '£', '～', '_', ';', '\n', ']', '§', '！', '>', '!', '+', '%', '@', '’', ',', '#', '·', '\x0c', '¥'}
    if char in common_symbols:
        return True

    return False

def calculate_file_non_allowed_ratio(text: str) -> tuple:
    """计算整个文件中非法字符的占比"""
    non_space_chars = [char for char in text if not char.isspace()]
    if not non_space_chars:
        return 0.0, set(), 0, 0

    total_chars = len(non_space_chars)
    non_allowed_chars = set()
    non_allowed_count = 0

    for char in text:
        if not char.isspace() and not is_allowed_character(char):
            non_allowed_chars.add(char)
            non_allowed_count += 1

    ratio = non_allowed_count / total_chars if total_chars > 0 else 0
    return ratio, non_allowed_chars, non_allowed_count, total_chars

def retry_operation(operation: Callable, max_retries: int = 3, delay: float = 1.0, *args, **kwargs) -> Optional[Dict[str, Any]]:
    """通用重试函数"""
    for retry in range(max_retries):
        try:
            result = operation(*args, **kwargs)

            # 检查是否有有效结果
            if result and result.get("rewritten_text"):
                rewritten_text = result.get("rewritten_text")

                # 移除括号内容
                rewritten_text = re.sub(r"（.*?）", "", rewritten_text)
                rewritten_text = re.sub(r"\(.*?\)", "", rewritten_text)

                ratio, non_allowed_chars, non_allowed_count, total_chars = calculate_file_non_allowed_ratio(rewritten_text)
                threshold = 0.02  # 2% 阈值，调整为更宽松的设置

                # 计算英文字母占比
                english_chars = sum(1 for char in rewritten_text if 'a' <= char.lower() <= 'z')
                english_ratio = english_chars / total_chars if total_chars > 0 else 0
                english_threshold = 0.10  # 10% 阈值，调整为更宽松的设置

                # 检查质量 - 调整字符数阈值，使其更宽松
                min_chars = max(200, len(args[0]) * 0.3) if args else 50  # 动态设置最小字符数

                # 添加调试信息
                if non_allowed_chars:
                    logger.warning(f"检测到非法字符: {', '.join(list(non_allowed_chars)[:10])}{'...' if len(non_allowed_chars) > 10 else ''}")
                    logger.warning(f"非法字符数量: {non_allowed_count}, 总字符数: {total_chars}, 占比: {ratio:.2%}")

                if total_chars < min_chars:
                    logger.warning(f"总字符数{total_chars}小于最小要求{min_chars}，需要重试。")
                elif ratio >= threshold:
                    logger.warning(f"非法字符占比为 {ratio:.2%}，超过阈值 {threshold:.2%}，需要重试。")
                elif english_ratio >= english_threshold:
                    logger.warning(f"英文字母占比为 {english_ratio:.2%}，超过阈值 {english_threshold:.2%}，需要重试。")
                else:
                    logger.info(f"文本质量检查通过：字符数{total_chars}，非法字符占比{ratio:.2%}，英文占比{english_ratio:.2%}")
                    return result

        except BlockError as e:
            logger.warning(f"BlockError in {operation.__name__}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error during {operation.__name__}: {str(e)}")

        if retry < max_retries - 1:
            logger.info(f"Retrying {operation.__name__}, attempt {retry + 2}/{max_retries}")
            time.sleep(delay)
        else:
            # 最后一次重试失败，检查是否有部分可用的结果
            if result and result.get("rewritten_text"):
                logger.warning(f"最后一次重试，接受质量较低的结果：{len(result.get('rewritten_text', ''))}字符")
                return result

    logger.error(f"所有重试都失败了，{operation.__name__}返回None")
    return None

class TextRewriter:
    def __init__(self, api_keys: List[str], character: str, book_name: str, channel: str, person: str, key_func):
        """Initialize the TextRewriter with API keys and model configuration."""
        self.client = None
        self.model = None
        self.api_keys = api_keys
        self.current_key_index = 0
        self.model_name = "gemini-2.5-pro-preview-06-05"
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func

        # 初始化Jinja2模板环境
        template_dir = Path(__file__).parent / 'prompt_templates'
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

        self.configure_api()

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        template = self.jinja_env.get_template('system_prompt.j2')
        return template.render(
            person=self.person,
            channel=self.channel
        )

    def configure_api(self) -> None:
        # 从配置中获取API密钥
        api_key = current_app.config.get('API_KEYS', [''])[0] if current_app.config.get('API_KEYS') else ''
        self.client = OpenAI(
            api_key=api_key,
            base_url='https://oapi.xmly.dev/v1'
        )

    def send_chat_completion(self, messages: List[Dict[str, str]], max_retries: int = 1) -> Optional[str]:
        for retry in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=1,
                    top_p=0.95,
                    max_tokens=32000,
                    timeout=800,
                )

                if 'No candidates returned' in str(response):
                    raise BlockError

                # 过滤掉<think></think>标签及其内容
                content = response.choices[0].message.content
                filtered_content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

                return filtered_content

            except BlockError as e:
                raise e
            except Exception as e:
                logger.error(f"API error: {str(e)}")
                if retry < max_retries - 1:
                    self.rotate_api_key()
                else:
                    return None

    def rotate_api_key(self) -> None:
        """Rotate to the next API key."""
        self.configure_api()

    def find_similar_chunks(self, text2: str, text1: str, chunk_size: int = 200, overlap: int = 50,
                           threshold: float = 0.3, min_pairs: int = 5, max_pairs: int = 10) -> List:
        """Find similar chunks between two texts using SequenceMatcher."""
        try:
            # 先移除特定字符，再移除空白字符
            texts = []
            for text in [text2, text1]:
                text = re.sub(r'[「」""''\u3000]', '', text)
                text = re.sub(r'\s', '', text)
                texts.append(text)

            def create_chunks(text: str) -> List[str]:
                sentences = re.split(r'([。！？])', text)
                sentences = [''.join(i) for i in zip(sentences[0::2], sentences[1::2] + [''])]
                sentences = [s for s in sentences if s.strip()]

                chunks = []
                current_chunk = []
                current_length = 0

                for sentence in sentences:
                    sentence_length = len(sentence)

                    if current_length + sentence_length > chunk_size and current_chunk:
                        chunks.append(''.join(current_chunk))
                        current_chunk = [current_chunk[-1]] if current_chunk else []
                        current_length = len(current_chunk[0]) if current_chunk else 0

                    current_chunk.append(sentence)
                    current_length += sentence_length

                if current_chunk:
                    chunks.append(''.join(current_chunk))

                return chunks

            chunks_pairs = [create_chunks(text) for text in texts]

            if not chunks_pairs[0] or not chunks_pairs[1]:
                return []

            # 存储所有相似度超过阈值的配对
            all_pairs = []

            for chunk1 in chunks_pairs[0]:
                chunk_pairs = []
                for chunk2 in chunks_pairs[1]:
                    similarity = SequenceMatcher(None, chunk1, chunk2).ratio()
                    if similarity > threshold:
                        chunk_pairs.append((chunk1, chunk2, similarity))

                # 如果当前chunk1有匹配项，选择相似度最高的一个
                if chunk_pairs:
                    best_pair = max(chunk_pairs, key=lambda x: x[2])
                    all_pairs.append(best_pair)

            # 按相似度降序排序
            all_pairs.sort(key=lambda x: x[2], reverse=True)

            # 如果匹配数量少于min_pairs且有门槛值限制，逐步降低阈值直到满足最小数量
            if len(all_pairs) < min_pairs and threshold > 0.1:
                return self.find_similar_chunks(
                    text2, text1, chunk_size, overlap,
                    max(threshold - 0.1, 0.1),  # 不要低于0.1
                    min_pairs, max_pairs
                )

            # 返回前max_pairs个结果，但如果结果数量小于min_pairs则返回所有结果
            return all_pairs[:max_pairs] if len(all_pairs) >= min_pairs else all_pairs

        except Exception as e:
            logging.error(f"Error in finding similar chunks: {str(e)}")
            return []

    def initial_rewrite(self, text: str, pre: str) -> Dict[str, str]:
        """First rewrite of the input text using the Gemini model."""
        template = self.jinja_env.get_template('initial_prompt.j2')
        initial_prompt = template.render(
            pre_text=pre,
            character=self.character,
            text_length=len(text),
            min_output_length=int(len(text) * 0.7),
            max_output_length=int(len(text) * 0.8),
            text=text
        )

        logger.info(f"-------------------Initial rewriting-------------------")
        try:
            logger.info(f"Initial rewriting context length: {len(text)}")
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": initial_prompt}
            ]
            response = self.send_chat_completion(messages)
            if response:
                logger.info(f"Initial rewriting result length: {len(response)}")
                logger.debug(f"Initial rewriting result preview: {response[:200]}...")
            else:
                logger.warning("Initial rewriting returned empty response")
            return {
                "rewritten_text": response,
                "original_prompt": initial_prompt
            } if response else {"rewritten_text": None, "original_prompt": initial_prompt}
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during initial rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": initial_prompt}

    def optimize_rewrite(self, original_text: str, first_rewrite: str) -> Dict[str, str]:
        """Optimize the first rewrite using the Gemini model."""
        template = self.jinja_env.get_template('optimize_prompt.j2')
        review_prompt = template.render(
            original_text=original_text,
            first_rewrite=first_rewrite
        )

        logger.info(f"-------------------Optimization rewriting-------------------")
        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": review_prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Optimization rewriting result length: {len(response) if response else 0}")
            return {
                "rewritten_text": response,
                "original_prompt": review_prompt
            }
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during optimization rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None, "original_prompt": review_prompt}

    def further_rewrite(self, original_text: str, origin_prompt: str, rewritten_text: str) -> Dict[str, str]:
        """Perform additional rewriting based on similar text chunks."""
        similar_chunks = self.find_similar_chunks(rewritten_text, original_text)
        logger.info(f"-------------------Further rewriting-------------------")
        if not similar_chunks:
            logger.info("No text chunks requiring further rewriting found.")
            return {"rewritten_text": rewritten_text}

        # 准备相似文本块数据
        chunk_data = []
        for rewrite, _, sim in similar_chunks:
            clean_text = rewrite.replace('\n', ' ')
            chunk_data.append({
                'text': clean_text,
                'similarity': sim
            })

        template = self.jinja_env.get_template('further_rewrite_prompt.j2')
        prompt = template.render(similar_chunks=chunk_data)

        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": origin_prompt},
                {"role": "assistant", "content": rewritten_text},
                {"role": "user", "content": prompt}
            ]
            response = self.send_chat_completion(messages)
            logger.info(f"Further rewriting result length: {len(response) if response else 0}")
            return {"rewritten_text": response}

        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during further rewriting: {str(e)}")
            self.rotate_api_key()
            return {"rewritten_text": None}

    def _process_combined_text(self, combined_text: str, pre_text: str, num_attempts: int, batch_name: str) -> Optional[str]:
        """处理合并后的文本并返回最佳结果"""
        results = []
        max_attempts = num_attempts * 2
        attempts = 0

        while len(results) < num_attempts and attempts < max_attempts:
            attempts += 1
            logger.info(f"Attempt {attempts} for {batch_name}")

            # 尝试重写
            result = self._attempt_rewrite_combined(combined_text, pre_text, attempts)
            if result:
                results.append(result)
                logger.info(f"Successful attempt {attempts} for {batch_name} finished with {len(result)} characters.")

        if not results:
            logger.error(f"Failed to rewrite {batch_name} after {max_attempts} attempts")
            # 降级策略：返回原文本而不是None
            logger.warning(f"使用降级策略：返回原始文本作为{batch_name}的结果")
            return combined_text

        # 返回最长的结果
        best_result = max(results, key=lambda x: len(x))
        logger.info(f"成功处理{batch_name}，选择了长度为{len(best_result)}的最佳结果")
        return best_result

    def _attempt_rewrite_combined(self, combined_text: str, pre_text: str, attempt_num: int,
                                enable_optimization: bool = True, max_retries: int = 10, delay: float = 1.0) -> Optional[str]:
        """尝试一次合并文本的重写"""
        # 尝试initial_rewrite
        initial_result = retry_operation(
            self.initial_rewrite,
            max_retries,
            delay,
            combined_text,
            pre_text
        )
        if not initial_result:
            logger.error(f"Initial rewriting failed after {max_retries} retries for combined text")
            return None

        if enable_optimization:
            # 尝试optimize_rewrite
            optimized_result = retry_operation(
                self.optimize_rewrite,
                max_retries,
                delay,
                combined_text,
                initial_result["rewritten_text"]
            )
            if not optimized_result:
                logger.error(f"Optimization failed after {max_retries} retries for combined text")
                optimized_result = {"rewritten_text": initial_result["rewritten_text"]}
        else:
            optimized_result = {"rewritten_text": initial_result["rewritten_text"]}

        # 尝试further_rewrite
        further_result = retry_operation(
            self.further_rewrite,
            max_retries,
            delay,
            combined_text,
            initial_result['original_prompt'],
            optimized_result["rewritten_text"]
        )
        if not further_result:
            logger.error(f"Further rewriting failed after {max_retries} retries for combined text")
            further_result = {"rewritten_text": initial_result["rewritten_text"]}

        return further_result["rewritten_text"]

class ProgressAwareTextRewriter(TextRewriter):
    """支持进度回调的TextRewriter"""

    def __init__(self, *args, **kwargs):
        self.progress_callback = kwargs.pop('progress_callback', None)
        super().__init__(*args, **kwargs)

    def process_directory_with_progress(self, input_dir, output_dir=None, num_attempts=2,
                                      start_chapter=None, end_chapter=None,
                                      chapters_per_batch=1, progress_callback=None):
        """带进度回调的目录处理方法"""
        if progress_callback:
            self.progress_callback = progress_callback

        # 复制原有逻辑，但添加进度回调
        input_path = Path(input_dir)
        output_path = Path(output_dir) if output_dir else input_path
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有.txt文件但排除包含rewrite的文件
        input_files = sorted(
            [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem],
            key=lambda x: int(re.search(r'\d+', x.stem).group())
        )

        # 应用章节过滤
        if start_chapter is not None or end_chapter is not None:
            filtered_files = []
            for f in input_files:
                chapter_num = int(re.search(r'\d+', f.stem).group())
                if start_chapter is not None and chapter_num < start_chapter:
                    continue
                if end_chapter is not None and chapter_num > end_chapter:
                    continue
                filtered_files.append(f)
            input_files = filtered_files

        if not input_files:
            if self.progress_callback:
                self.progress_callback(0, 0, f"没有找到要处理的文件")
            return

        total_batches = (len(input_files) + chapters_per_batch - 1) // chapters_per_batch
        processed_batches = 0

        # 按批次处理文件
        for i in range(0, len(input_files), chapters_per_batch):
            batch_files = input_files[i:i+chapters_per_batch]

            # 获取批次信息
            first_chapter_num = int(re.search(r'\d+', batch_files[0].stem).group())
            last_chapter_num = int(re.search(r'\d+', batch_files[-1].stem).group())

            if self.progress_callback:
                self.progress_callback(
                    processed_batches,
                    total_batches,
                    f"处理第{first_chapter_num}-{last_chapter_num}章..."
                )

            # 检查是否已经处理过这个批次
            batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
            batch_output_path = output_path / batch_output_name

            if batch_output_path.exists():
                logger.info(f"Skipping already processed batch: {batch_output_name}")
                processed_batches += 1
                continue

            # 处理批次（使用原有逻辑）
            try:
                self._process_batch(batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts)
                processed_batches += 1

                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"完成第{first_chapter_num}-{last_chapter_num}章"
                    )
            except Exception as e:
                logger.error(f"处理批次失败: {str(e)}")
                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"处理第{first_chapter_num}-{last_chapter_num}章失败: {str(e)}"
                    )
                raise

    def _process_batch(self, batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts):
        """处理单个批次"""
        # 获取pre_text
        pre_text = '无前文'
        if first_chapter_num > 1:
            # 查找上一个批次的输出文件
            previous_batch_files = sorted(
                [f for f in output_path.glob('*_rewrite.txt')],
                key=lambda x: int(re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name).group(1)) if re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name) else 0
            )

            if previous_batch_files:
                # 取最后一个处理过的批次文件的后500字符作为pre_text
                last_batch_file = previous_batch_files[-1]
                try:
                    pre_text = last_batch_file.read_text(encoding='utf-8')[-500:]
                    logger.info(f"Retrieved pre_text from batch: {last_batch_file.name}")
                except Exception as e:
                    logger.error(f"Error reading batch file {last_batch_file.name}: {str(e)}")
                    pre_text = '无前文'

        # 合并当前批次的所有章节内容
        combined_text = ""
        chapter_info = []

        for file_path in batch_files:
            try:
                chapter_text = file_path.read_text(encoding='utf-8')
                chapter_num = int(re.search(r'\d+', file_path.stem).group())
                combined_text += f"\n\n=== 第{chapter_num}章 ===\n\n{chapter_text}"
                chapter_info.append(f"第{chapter_num}章")
                logger.info(f"Added chapter {chapter_num} to batch (length: {len(chapter_text)} characters)")
            except Exception as e:
                logger.error(f"Error reading file {file_path.name}: {str(e)}")
                continue

        if not combined_text:
            raise Exception(f"No valid content found in batch starting with chapter {first_chapter_num}")

        logger.info(f"Processing batch: {' + '.join(chapter_info)} (total length: {len(combined_text)} characters)")

        # 处理合并后的文本
        best_result = self._process_combined_text(combined_text, pre_text, num_attempts, f"batch_{first_chapter_num}_{last_chapter_num}")

        # 保存结果（best_result现在总是有值，要么是处理结果，要么是原文）
        batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
        batch_output_path = output_path / batch_output_name
        batch_output_path.write_text(best_result, encoding='utf-8')
        logger.info(f"Successfully processed and saved batch: {batch_output_name} (length: {len(best_result)} characters)")

    def process_directory_with_concurrency(self, input_dir, output_dir=None, num_attempts=2,
                                         start_chapter=None, end_chapter=None,
                                         max_workers=3, progress_callback=None):
        """带并发支持的目录处理方法（一章一章处理）"""
        if progress_callback:
            self.progress_callback = progress_callback

        input_path = Path(input_dir)
        output_path = Path(output_dir) if output_dir else input_path
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有.txt文件但排除包含rewrite的文件
        input_files = sorted(
            [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem],
            key=lambda x: int(re.search(r'\d+', x.stem).group())
        )

        # 应用章节过滤
        if start_chapter is not None or end_chapter is not None:
            filtered_files = []
            for f in input_files:
                chapter_num = int(re.search(r'\d+', f.stem).group())
                if start_chapter is not None and chapter_num < start_chapter:
                    continue
                if end_chapter is not None and chapter_num > end_chapter:
                    continue
                filtered_files.append(f)
            input_files = filtered_files

        if not input_files:
            if self.progress_callback:
                self.progress_callback(0, 0, f"没有找到要处理的文件")
            return

        total_chapters = len(input_files)

        # 统计已处理的章节
        already_processed = 0
        pending_files = []

        for file_path in input_files:
            chapter_num = int(re.search(r'\d+', file_path.stem).group())
            output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
            output_file_path = output_path / output_name

            if output_file_path.exists():
                logger.info(f"Skipping already processed chapter: {chapter_num}")
                already_processed += 1
            else:
                pending_files.append(file_path)

        # 初始进度包含已处理的章节
        processed_chapters = already_processed

        if self.progress_callback and already_processed > 0:
            self.progress_callback(
                processed_chapters,
                total_chapters,
                f"发现{already_processed}个已处理章节，继续处理剩余{len(pending_files)}章"
            )

        if not pending_files:
            if self.progress_callback:
                self.progress_callback(total_chapters, total_chapters, "所有章节已处理完成")
            return

        # 使用改进的并发处理策略
        self._process_chapters_with_smart_concurrency(
            pending_files, output_path, num_attempts,
            processed_chapters, total_chapters, max_workers
        )

    def _process_chapters_with_smart_concurrency(self, pending_files, output_path, num_attempts,
                                               initial_processed, total_chapters, max_workers):
        """智能并发处理章节，考虑章节间的依赖关系"""
        import threading


        processed_chapters = initial_processed
        completed_chapters = set()  # 记录已完成的章节号
        processing_lock = threading.Lock()  # 用于线程安全的计数更新

        # 按章节号分组，确保顺序处理的依赖关系
        chapter_files = {}
        for file_path in pending_files:
            chapter_num = int(re.search(r'\d+', file_path.stem).group())
            chapter_files[chapter_num] = file_path

        # 获取所有需要处理的章节号，按顺序排列
        chapter_numbers = sorted(chapter_files.keys())

        def process_chapter_with_dependency(chapter_num):
            """处理单个章节，考虑前置依赖"""
            file_path = chapter_files[chapter_num]

            try:
                # 等待前一章完成（如果需要）
                if chapter_num > 1:
                    self._wait_for_previous_chapter(chapter_num - 1, output_path, completed_chapters, timeout=300)

                # 处理当前章节
                result = self._process_single_chapter_with_smart_context(
                    file_path, output_path, num_attempts, chapter_num
                )

                # 线程安全地更新进度
                with processing_lock:
                    nonlocal processed_chapters
                    processed_chapters += 1
                    completed_chapters.add(chapter_num)

                    if self.progress_callback:
                        self.progress_callback(
                            processed_chapters,
                            total_chapters,
                            f"完成第{chapter_num}章处理"
                        )

                logger.info(f"Successfully processed chapter {chapter_num}")
                return result

            except Exception as e:
                logger.error(f"处理第{chapter_num}章失败: {str(e)}")
                with processing_lock:
                    if self.progress_callback:
                        self.progress_callback(
                            processed_chapters,
                            total_chapters,
                            f"第{chapter_num}章处理失败: {str(e)}"
                        )
                raise

        # 使用线程池处理，但限制并发数以避免过多等待
        with ThreadPoolExecutor(max_workers=min(max_workers, 2)) as executor:
            # 提交所有任务
            futures = []
            for chapter_num in chapter_numbers:
                future = executor.submit(process_chapter_with_dependency, chapter_num)
                futures.append((chapter_num, future))

            # 等待所有任务完成
            for chapter_num, future in futures:
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Chapter {chapter_num} processing failed: {str(e)}")

    def _wait_for_previous_chapter(self, prev_chapter_num, output_path, completed_chapters, timeout=300):
        """等待前一章完成处理"""
        import time

        # 如果前一章已经完成，直接返回
        if prev_chapter_num in completed_chapters:
            return True

        # 检查前一章的输出文件是否存在
        prev_output_name = f"chapter_{prev_chapter_num:03d}_rewrite.txt"
        prev_output_path = output_path / prev_output_name

        if prev_output_path.exists():
            completed_chapters.add(prev_chapter_num)
            return True

        # 等待前一章完成，最多等待timeout秒
        start_time = time.time()
        while time.time() - start_time < timeout:
            if prev_chapter_num in completed_chapters or prev_output_path.exists():
                completed_chapters.add(prev_chapter_num)
                return True
            time.sleep(1)  # 每秒检查一次

        logger.warning(f"等待第{prev_chapter_num}章完成超时，继续处理")
        return False

    def _process_single_chapter_with_smart_context(self, file_path, output_path, num_attempts, chapter_num):
        """智能上下文的单章节处理"""
        # 获取智能前文
        pre_text = self._get_smart_previous_context(output_path, chapter_num)

        # 读取章节内容
        try:
            chapter_text = file_path.read_text(encoding='utf-8')
            logger.info(f"Processing chapter {chapter_num} with smart context (length: {len(chapter_text)} characters)")
        except Exception as e:
            logger.error(f"Error reading chapter {chapter_num}: {str(e)}")
            raise

        # 处理章节内容
        best_result = self._process_combined_text(
            chapter_text,
            pre_text,
            num_attempts,
            f"chapter_{chapter_num}"
        )

        # 保存结果
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name
        output_file_path.write_text(best_result, encoding='utf-8')

        logger.info(f"Successfully saved chapter {chapter_num}: {output_name} (length: {len(best_result)} characters)")
        return best_result

    def _get_smart_previous_context(self, output_path, current_chapter_num):
        """智能获取前文上下文 - 优化并发处理版本"""
        if current_chapter_num <= 1:
            return '无前文'

        # 直接使用原始章节文件，支持真正的并发处理
        prev_chapter_num = current_chapter_num - 1
        original_prev_name = f"chapter_{prev_chapter_num:03d}.txt"
        original_prev_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / original_prev_name

        if original_prev_path.exists():
            try:
                prev_text = original_prev_path.read_text(encoding='utf-8')
                context = self._extract_smart_ending(prev_text)
                logger.info(f"使用第{prev_chapter_num}章原始文本作为上下文 (长度: {len(context)}字)")
                return context
            except Exception as e:
                logger.error(f"Error reading original chapter {prev_chapter_num}: {str(e)}")

        # 备用策略: 尝试获取更前面的原始章节
        for i in range(2, min(4, current_chapter_num)):  # 最多往前找2章
            alt_chapter_num = current_chapter_num - i
            alt_original_name = f"chapter_{alt_chapter_num:03d}.txt"
            alt_original_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / alt_original_name

            if alt_original_path.exists():
                try:
                    alt_text = alt_original_path.read_text(encoding='utf-8')
                    context = self._extract_smart_ending(alt_text)
                    logger.info(f"使用第{alt_chapter_num}章原始文本作为替代上下文 (长度: {len(context)}字)")
                    return f"[前文摘要] {context}"
                except Exception as e:
                    continue

        return '无前文'

    def _extract_smart_ending(self, text):
        """智能提取文本结尾部分作为上下文"""
        if not text:
            return '无前文'

        # 按段落分割
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        if not paragraphs:
            # 如果没有段落分割，直接取最后800字符
            return text[-800:] if len(text) > 800 else text

        # 策略：取最后2-3个段落，但不超过1000字符
        result_paragraphs = []
        total_length = 0
        max_length = 1000
        max_paragraphs = 3

        for paragraph in reversed(paragraphs[-max_paragraphs:]):
            if total_length + len(paragraph) <= max_length:
                result_paragraphs.insert(0, paragraph)
                total_length += len(paragraph)
            else:
                # 如果当前段落太长，截取部分
                remaining = max_length - total_length
                if remaining > 100:  # 至少保留100字符
                    truncated = paragraph[-remaining:]
                    result_paragraphs.insert(0, f"...{truncated}")
                break

        return '\n\n'.join(result_paragraphs) if result_paragraphs else text[-800:]

    def _process_single_chapter_concurrent(self, file_path, output_path, num_attempts):
        """并发处理单个章节"""
        chapter_num = int(re.search(r'\d+', file_path.stem).group())

        # 获取前文
        pre_text = self._get_previous_chapter_text(output_path, chapter_num)

        # 读取章节内容
        try:
            chapter_text = file_path.read_text(encoding='utf-8')
            logger.info(f"Processing chapter {chapter_num} (length: {len(chapter_text)} characters)")
        except Exception as e:
            logger.error(f"Error reading chapter {chapter_num}: {str(e)}")
            raise

        # 处理章节内容
        best_result = self._process_combined_text(
            chapter_text,
            pre_text,
            num_attempts,
            f"chapter_{chapter_num}"
        )

        # 保存结果
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name
        output_file_path.write_text(best_result, encoding='utf-8')

        logger.info(f"Successfully saved chapter {chapter_num}: {output_name} (length: {len(best_result)} characters)")
        return best_result

    def _get_previous_chapter_text(self, output_path, current_chapter_num):
        """获取前一章的文本作为上下文"""
        if current_chapter_num <= 1:
            return '无前文'

        # 查找前一章的输出文件
        prev_chapter_num = current_chapter_num - 1
        prev_output_name = f"chapter_{prev_chapter_num:03d}_rewrite.txt"
        prev_output_path = output_path / prev_output_name

        if prev_output_path.exists():
            try:
                prev_text = prev_output_path.read_text(encoding='utf-8')
                # 取最后500字符作为上下文
                return prev_text[-500:] if len(prev_text) > 500 else prev_text
            except Exception as e:
                logger.error(f"Error reading previous chapter {prev_chapter_num}: {str(e)}")
                return '无前文'
        else:
            # 如果前一章的优化版本不存在，尝试读取原始章节文件
            original_prev_name = f"chapter_{prev_chapter_num:03d}.txt"
            original_prev_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / original_prev_name

            if original_prev_path.exists():
                try:
                    prev_text = original_prev_path.read_text(encoding='utf-8')
                    return prev_text[-500:] if len(prev_text) > 500 else prev_text
                except Exception as e:
                    logger.error(f"Error reading original previous chapter {prev_chapter_num}: {str(e)}")
                    return '无前文'

            return '无前文'


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理故事优化任务"""
    try:
        # 获取任务信息
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")
        
        # 更新任务状态
        task.status = 'processing'
        task.started_at = datetime.utcnow()
        task.celery_task_id = self.request.id
        db.session.commit()
        
        # 更新进度
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})
        
        # 读取原始文件
        with open(task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 分割章节
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)
        
        if not chapters:
            raise Exception("无法识别章节结构")
        
        # 更新总章节数
        task.total_chapters = len(chapters)
        db.session.commit()
        
        # 创建章节文件
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, _ = create_chapter_files(chapters, task.file_path)
        
        # 初始化TextRewriter
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化故事优化引擎...'})

        api_keys = current_app.config['API_KEYS']
        rewriter = ProgressAwareTextRewriter(
            api_keys=api_keys,
            character=task.character,
            book_name=task.book_name,
            channel=task.channel,
            person=task.person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )
        
        # 设置输出目录
        output_dir = Path(task.file_path).parent / f"{Path(task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 开始故事优化处理
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始故事优化处理...'})
        
        # 使用并发处理方法进行故事优化
        try:
            # 使用新的并发处理方法，一章一章处理并支持并发
            rewriter.process_directory_with_concurrency(
                input_dir=chapters_dir,
                output_dir=str(output_dir),
                num_attempts=1,
                start_chapter=getattr(task, 'start_chapter', None),
                end_chapter=getattr(task, 'end_chapter', None),
                max_workers=3,  # 并发处理3个章节
                progress_callback=lambda current, total, status: update_task_progress(
                    self, task, current, total, status, 40, 90
                )
            )
        except Exception as e:
            logger.error(f"故事优化处理失败: {str(e)}")
            raise
        
        # 合并输出文件
        self.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, task)
        
        # 更新任务完成状态
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        task.output_path = final_output_path
        task.progress = 100
        db.session.commit()
        
        self.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '故事优化完成！'})
        
        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task.total_chapters,
            'processed_chapters': task.processed_chapters
        }
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"任务处理失败: {error_msg}")

        # 更新任务失败状态
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = error_msg
            task.completed_at = datetime.utcnow()
            db.session.commit()

        # 使用字符串而不是异常对象来避免序列化问题
        self.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
        # 抛出一个简单的异常，避免复杂对象序列化问题
        raise Exception(error_msg)

def update_task_progress(celery_task, db_task, current, total, status, min_progress, max_progress):
    """更新任务进度"""
    # 计算实际进度（在min_progress和max_progress之间）
    if total > 0:
        progress_ratio = current / total
        actual_progress = min_progress + (max_progress - min_progress) * progress_ratio
    else:
        actual_progress = min_progress
    
    # 更新数据库
    db_task.progress = int(actual_progress)
    db_task.processed_chapters = current
    db.session.commit()
    
    # 更新Celery状态
    celery_task.update_state(
        state='PROGRESS',
        meta={
            'current': int(actual_progress),
            'total': 100,
            'status': status,
            'chapters_current': current,
            'chapters_total': total
        }
    )

def merge_adapted_files(output_dir, task):
    """合并故事优化后的文件"""
    # 按章节号排序文件
    output_files = []
    for file_path in Path(output_dir).glob('*_rewrite.txt'):
        # 提取章节号进行排序
        match = re.search(r'chapter_(\d+)', file_path.name)
        if match:
            chapter_num = int(match.group(1))
            output_files.append((chapter_num, file_path))

    # 按章节号排序
    output_files.sort(key=lambda x: x[0])

    if not output_files:
        raise Exception("没有找到优化后的文件")

    # 创建最终输出文件
    final_filename = f"{task.task_name}_adapted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    final_path = Path(output_dir).parent / final_filename

    with open(final_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"《{task.book_name}》优化版\n")
        outfile.write(f"主角：{task.character}\n")
        outfile.write(f"频道：{task.channel}\n")
        outfile.write(f"人称：第{task.person}人称\n")
        outfile.write(f"优化时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 50 + "\n\n")

        for chapter_num, file_path in output_files:
            with open(file_path, 'r', encoding='utf-8') as infile:
                content = infile.read().strip()
                outfile.write(f"\n\n=== 第{chapter_num}章 ===\n\n")
                outfile.write(content)
                outfile.write("\n\n")

    return str(final_path)
