#!/usr/bin/env python3
"""
简单测试并发改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        # 测试智能上下文提取逻辑
        test_text = """第一段内容，这里是故事的开始部分。

第二段内容，情节开始发展。

第三段内容，这是故事的高潮部分。

第四段内容，故事接近尾声。

第五段内容，这是本章的结尾。"""
        
        # 模拟智能提取逻辑
        paragraphs = [p.strip() for p in test_text.split('\n\n') if p.strip()]
        print(f"原文段落数: {len(paragraphs)}")
        
        # 取最后2-3个段落
        result_paragraphs = []
        total_length = 0
        max_length = 1000
        max_paragraphs = 3
        
        for paragraph in reversed(paragraphs[-max_paragraphs:]):
            if total_length + len(paragraph) <= max_length:
                result_paragraphs.insert(0, paragraph)
                total_length += len(paragraph)
            else:
                remaining = max_length - total_length
                if remaining > 100:
                    truncated = paragraph[-remaining:]
                    result_paragraphs.insert(0, f"...{truncated}")
                break
        
        context = '\n\n'.join(result_paragraphs) if result_paragraphs else test_text[-800:]
        
        print(f"提取的上下文长度: {len(context)}字")
        print(f"提取的上下文:\n{context}")
        print("✓ 智能上下文提取逻辑正确")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_chapter_counting_logic():
    """测试章节计数逻辑"""
    print("\n=== 测试章节计数逻辑 ===")
    
    try:
        # 模拟章节文件列表
        all_chapters = [1, 2, 3, 4, 5]
        processed_chapters = [1, 3]  # 已处理的章节
        
        total_chapters = len(all_chapters)
        already_processed = len(processed_chapters)
        pending_chapters = [ch for ch in all_chapters if ch not in processed_chapters]
        
        print(f"总章节数: {total_chapters}")
        print(f"已处理章节: {already_processed} (章节: {processed_chapters})")
        print(f"待处理章节: {len(pending_chapters)} (章节: {pending_chapters})")
        
        # 模拟处理进度更新
        current_processed = already_processed
        for chapter in pending_chapters:
            current_processed += 1
            progress_percent = (current_processed / total_chapters) * 100
            print(f"处理第{chapter}章完成，进度: {current_processed}/{total_chapters} ({progress_percent:.1f}%)")
        
        print("✓ 章节计数逻辑正确")
        
    except Exception as e:
        print(f"章节计数测试失败: {e}")

def test_dependency_waiting():
    """测试依赖等待逻辑"""
    print("\n=== 测试依赖等待逻辑 ===")
    
    try:
        # 模拟章节完成状态
        completed_chapters = set()
        
        def check_previous_chapter(current_chapter, completed_set):
            """检查前一章是否完成"""
            if current_chapter <= 1:
                return True, "第1章无需等待"
            
            prev_chapter = current_chapter - 1
            if prev_chapter in completed_set:
                return True, f"第{prev_chapter}章已完成"
            else:
                return False, f"第{prev_chapter}章未完成，需要等待"
        
        # 测试不同场景
        test_cases = [
            (1, set()),  # 第1章
            (2, {1}),    # 第2章，第1章已完成
            (3, {1}),    # 第3章，第1章已完成但第2章未完成
            (4, {1, 2, 3})  # 第4章，前面都已完成
        ]
        
        for chapter, completed in test_cases:
            can_proceed, reason = check_previous_chapter(chapter, completed)
            print(f"第{chapter}章: {'可以处理' if can_proceed else '需要等待'} - {reason}")
        
        print("✓ 依赖等待逻辑正确")
        
    except Exception as e:
        print(f"依赖等待测试失败: {e}")

if __name__ == "__main__":
    test_basic_functionality()
    test_chapter_counting_logic()
    test_dependency_waiting()
    print("\n=== 简单测试完成 ===")
