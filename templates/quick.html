{% extends "base.html" %}

{% block title %}快速优化 - 故事讲述器{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-lightning"></i> 快速优化
                    <small class="ms-2 opacity-75">直接粘贴内容，立即开始故事优化</small>
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="quickForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- 内容输入区域 -->
                    <div class="mb-4">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else ""), 
                                       style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6;") }}
                        {% if form.content.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.content.errors %}{{ error }}{% endfor %}
                            </div>
                        {% else %}
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i> 
                                支持50-10000字的内容，建议单章或片段优化效果最佳
                            </div>
                        {% endif %}
                        <div class="mt-2">
                            <small class="text-muted">
                                字数统计: <span id="charCount">0</span> / 10000
                            </small>
                        </div>
                    </div>

                    <!-- 优化参数 -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.character.label(class="form-label") }}
                            {{ form.character(class="form-control" + (" is-invalid" if form.character.errors else "")) }}
                            {% if form.character.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.character.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.book_name.label(class="form-label") }}
                            {{ form.book_name(class="form-control" + (" is-invalid" if form.book_name.errors else "")) }}
                            {% if form.book_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.book_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.channel.label(class="form-label") }}
                            {{ form.channel(class="form-select" + (" is-invalid" if form.channel.errors else "")) }}
                            {% if form.channel.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.channel.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.person.label(class="form-label") }}
                            {{ form.person(class="form-select" + (" is-invalid" if form.person.errors else "")) }}
                            {% if form.person.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.person.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 示例内容 -->
                    <div class="alert alert-light border mb-4">
                        <h6 class="alert-heading">
                            <i class="bi bi-lightbulb"></i> 示例内容
                        </h6>
                        <p class="mb-2">您可以粘贴以下类型的内容：</p>
                        <ul class="mb-0">
                            <li>小说章节片段</li>
                            <li>短篇故事</li>
                            <li>对话场景</li>
                            <li>情节描述</li>
                        </ul>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="loadExample()">
                            加载示例内容
                        </button>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                        {{ form.submit(class="btn btn-primary", id="submitBtn") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('content');
    const charCount = document.getElementById('charCount');
    const submitBtn = document.getElementById('submitBtn');
    const quickForm = document.getElementById('quickForm');

    // 字数统计
    function updateCharCount() {
        const count = contentTextarea.value.length;
        charCount.textContent = count;
        
        if (count > 10000) {
            charCount.className = 'text-danger';
        } else if (count < 50) {
            charCount.className = 'text-muted';
        } else {
            charCount.className = 'text-success';
        }
    }

    contentTextarea.addEventListener('input', updateCharCount);
    updateCharCount(); // 初始化

    // 表单提交
    quickForm.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 处理中...';
    });
});

// 加载示例内容
function loadExample() {
    const exampleContent = `第一章 穿越异世

李明睁开眼睛，发现自己躺在一个陌生的房间里。

"这是哪里？"他疑惑地坐起身来，环顾四周。

房间很简陋，只有一张木床、一张桌子和一把椅子。墙上挂着一面铜镜，镜子里的人让他大吃一惊。

"这不是我的脸！"

镜子里的人看起来只有十六七岁，面容清秀，但明显不是他原来的样子。

就在这时，一阵记忆涌入他的脑海。原来这个身体的主人也叫李明，是个孤儿，在这个叫做天元大陆的世界里艰难求生。

"穿越了？"李明不敢置信地摸着自己的脸。

突然，门外传来脚步声。

"李明，你醒了吗？"一个温和的声音响起。`;

    document.getElementById('content').value = exampleContent;
    document.getElementById('character').value = '李明';
    document.getElementById('book_name').value = '穿越修仙传';
    document.getElementById('channel').value = '男频';
    document.getElementById('person').value = '三';
    
    // 更新字数统计
    const event = new Event('input');
    document.getElementById('content').dispatchEvent(event);
}
</script>
{% endblock %}
