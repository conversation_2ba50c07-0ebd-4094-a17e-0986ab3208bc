#!/usr/bin/env python3
"""
检查用户数据和登录功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_users():
    """检查用户数据"""
    print("=== 检查用户数据 ===")
    
    try:
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            users = User.query.all()
            print(f"数据库中的用户数量: {len(users)}")
            
            for user in users:
                print(f"用户ID: {user.id}, 用户名: {user.username}, 活跃状态: {user.is_active}")
                
                # 测试密码验证
                test_passwords = ['password123', 'admin', '123456']
                for pwd in test_passwords:
                    if user.check_password(pwd):
                        print(f"  ✓ 密码 '{pwd}' 正确")
                        break
                else:
                    print(f"  ✗ 测试密码都不正确")
                    
    except Exception as e:
        print(f"检查用户失败: {e}")

def create_test_user():
    """创建测试用户"""
    print("\n=== 创建测试用户 ===")
    
    try:
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        
        with app.app_context():
            # 检查是否已存在admin用户
            admin_user = User.query.filter_by(username='admin').first()
            
            if admin_user:
                print("admin用户已存在")
                # 重置密码
                admin_user.set_password('password123')
                db.session.commit()
                print("✓ 重置admin密码为 'password123'")
            else:
                # 创建新的admin用户
                admin_user = User(username='admin')
                admin_user.set_password('password123')
                db.session.add(admin_user)
                db.session.commit()
                print("✓ 创建admin用户，密码为 'password123'")
                
            # 验证用户创建
            test_user = User.query.filter_by(username='admin').first()
            if test_user and test_user.check_password('password123'):
                print("✓ 用户创建和密码验证成功")
            else:
                print("✗ 用户创建或密码验证失败")
                
    except Exception as e:
        print(f"创建用户失败: {e}")

def test_login():
    """测试登录功能"""
    print("\n=== 测试登录功能 ===")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试登录页面
            login_page = client.get('/login')
            print(f"登录页面状态: {login_page.status_code}")
            
            if login_page.status_code == 200:
                # 提取CSRF token
                import re
                page_content = login_page.get_data(as_text=True)
                csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', page_content)
                
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"✓ 提取到CSRF token")
                    
                    # 尝试登录
                    login_data = {
                        'csrf_token': csrf_token,
                        'username': 'admin',
                        'password': 'password123',
                        'submit': '登录'
                    }
                    
                    login_response = client.post('/login', data=login_data, follow_redirects=False)
                    print(f"登录响应状态: {login_response.status_code}")
                    
                    if login_response.status_code == 302:
                        print("✓ 登录成功，重定向到dashboard")
                        
                        # 测试访问受保护的页面
                        dashboard_response = client.get('/dashboard')
                        print(f"Dashboard访问状态: {dashboard_response.status_code}")
                        
                        upload_response = client.get('/upload')
                        print(f"Upload页面访问状态: {upload_response.status_code}")
                        
                    else:
                        print("✗ 登录失败")
                        response_text = login_response.get_data(as_text=True)
                        if '用户名或密码错误' in response_text:
                            print("  原因: 用户名或密码错误")
                        else:
                            print(f"  响应内容: {response_text[:200]}...")
                else:
                    print("✗ 无法提取CSRF token")
            else:
                print("✗ 登录页面不可访问")
                
    except Exception as e:
        print(f"登录测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_upload_with_login():
    """测试登录后的上传功能"""
    print("\n=== 测试登录后的上传功能 ===")
    
    try:
        from app import create_app
        from io import BytesIO
        
        app = create_app()
        
        with app.test_client() as client:
            # 先登录
            login_page = client.get('/login')
            import re
            page_content = login_page.get_data(as_text=True)
            csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', page_content)
            
            if csrf_match:
                csrf_token = csrf_match.group(1)
                
                login_data = {
                    'csrf_token': csrf_token,
                    'username': 'admin',
                    'password': 'password123',
                    'submit': '登录'
                }
                
                login_response = client.post('/login', data=login_data, follow_redirects=False)
                
                if login_response.status_code == 302:
                    print("✓ 登录成功")
                    
                    # 获取上传页面
                    upload_page = client.get('/upload')
                    print(f"上传页面状态: {upload_page.status_code}")
                    
                    if upload_page.status_code == 200:
                        print("✓ 上传页面可访问")
                        
                        # 提取上传页面的CSRF token
                        upload_content = upload_page.get_data(as_text=True)
                        upload_csrf_match = re.search(r'name="csrf_token" type="hidden" value="([^"]+)"', upload_content)
                        
                        if upload_csrf_match:
                            upload_csrf_token = upload_csrf_match.group(1)
                            print("✓ 提取到上传页面CSRF token")
                            
                            # 准备上传数据
                            file_content = """第一章 测试开始

这是一个测试文件的内容。
主角开始了他的冒险之旅。

第二章 测试继续

故事继续发展。
主角遇到了新的挑战。
"""
                            
                            data = {
                                'csrf_token': upload_csrf_token,
                                'character': '测试主角',
                                'book_name': '测试小说',
                                'channel': '男频',
                                'person': '三',
                                'submit': '开始优化'
                            }
                            
                            files = {
                                'file': (BytesIO(file_content.encode('utf-8')), 'test_novel.txt', 'text/plain')
                            }
                            
                            # 提交上传
                            upload_response = client.post('/upload', 
                                                        data=data, 
                                                        content_type='multipart/form-data',
                                                        buffered=True)
                            
                            print(f"上传响应状态: {upload_response.status_code}")
                            
                            if upload_response.status_code == 302:
                                print("✓ 上传成功，重定向到任务详情页")
                                print(f"重定向位置: {upload_response.location}")
                            else:
                                print("✗ 上传失败")
                                response_text = upload_response.get_data(as_text=True)
                                print(f"响应内容: {response_text[:500]}...")
                        else:
                            print("✗ 无法提取上传页面CSRF token")
                    else:
                        print("✗ 上传页面不可访问")
                else:
                    print("✗ 登录失败")
            else:
                print("✗ 无法提取登录CSRF token")
                
    except Exception as e:
        print(f"上传测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_users()
    create_test_user()
    test_login()
    test_upload_with_login()
    print("\n=== 用户检查完成 ===")
